import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Target, TrendingUp, Medal, Layers } from "lucide-react";
import { MockTestAnalytics } from "@/types/mockTest";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface MockTestAnalyticsCardsProps {
  analytics: MockTestAnalytics;
}

export function MockTestAnalyticsCards({ analytics }: MockTestAnalyticsCardsProps) {
  // Function to determine color based on score
  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-500";
    if (score >= 75) return "text-green-400";
    if (score >= 60) return "text-yellow-500";
    if (score >= 40) return "text-orange-500";
    return "text-red-500";
  };

  // Function to determine background gradient based on score
  const getScoreGradient = (score: number) => {
    if (score >= 90) return "from-green-500/20 to-green-500/5";
    if (score >= 75) return "from-green-400/20 to-green-400/5";
    if (score >= 60) return "from-yellow-500/20 to-yellow-500/5";
    if (score >= 40) return "from-orange-500/20 to-orange-500/5";
    return "from-red-500/20 to-red-500/5";
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-gray-50 via-white to-gray-100/50 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative dark:from-gray-800 dark:via-gray-800 dark:to-gray-700/50">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-500/10 via-transparent to-gray-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 dark:from-gray-400/20 dark:via-transparent dark:to-gray-500/20" />
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
          <CardTitle className="text-sm font-semibold flex items-center gap-2 text-gray-700 dark:text-gray-300">
            <BookOpen className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            Total Tests
          </CardTitle>
          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 dark:from-gray-600 dark:to-gray-700">
            <Layers className="h-6 w-6 text-white" />
          </div>
        </CardHeader>
        <CardContent className="relative z-10">
          <div className="text-4xl font-bold text-gray-900 mb-2 dark:text-gray-100">{analytics.totalTests}</div>
          <p className="text-sm text-gray-600/70 font-medium dark:text-gray-300/70">
            Tests recorded in your history
          </p>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-emerald-50 via-white to-emerald-100/50 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative dark:from-emerald-900/20 dark:via-gray-800 dark:to-emerald-800/20">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-transparent to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 dark:from-emerald-400/20 dark:via-transparent dark:to-emerald-500/20" />
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
          <CardTitle className="text-sm font-semibold flex items-center gap-2 text-emerald-700 dark:text-emerald-300">
            <PieChart className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
            Average Score
          </CardTitle>
          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 dark:from-emerald-600 dark:to-emerald-700">
            <TrendingUp className="h-6 w-6 text-white" />
          </div>
        </CardHeader>
        <CardContent className="relative z-10">
          <div className={cn("text-4xl font-bold mb-2", getScoreColor(analytics.averageScore))}>
            {analytics.averageScore.toFixed(1)}%
          </div>
          <p className="text-sm text-emerald-600/70 font-medium dark:text-emerald-300/70">
            Average across all your tests
          </p>
          <div className="mt-3 h-2 w-full bg-emerald-200/30 rounded-full overflow-hidden shadow-inner dark:bg-emerald-800/30 relative">
            <div
              className="h-full rounded-full bg-gradient-to-r from-emerald-500 to-emerald-600 shadow-sm transition-all duration-700 ease-out"
              style={{ width: `${Math.min(100, analytics.averageScore)}%` }}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 animate-shimmer opacity-50" />
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-violet-50 via-white to-violet-100/50 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative dark:from-violet-900/20 dark:via-gray-800 dark:to-violet-800/20">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-500/10 via-transparent to-violet-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 dark:from-violet-400/20 dark:via-transparent dark:to-violet-500/20" />
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
          <CardTitle className="text-sm font-semibold flex items-center gap-2 text-violet-700 dark:text-violet-300">
            <Target className="h-5 w-5 text-violet-600 dark:text-violet-400" />
            Highest Score
          </CardTitle>
          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-violet-500 to-violet-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 dark:from-violet-600 dark:to-violet-700">
            <Medal className="h-6 w-6 text-white" />
          </div>
        </CardHeader>
        <CardContent className="relative z-10">
          <div className={cn("text-4xl font-bold mb-2", getScoreColor(analytics.highestScore.percentage))}>
            {analytics.highestScore.percentage.toFixed(1)}%
          </div>
          <p className="text-sm text-violet-600/70 font-medium line-clamp-1 dark:text-violet-300/70">
            {analytics.highestScore.testName || 'No tests recorded'}
          </p>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-rose-50 via-white to-rose-100/50 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative dark:from-rose-900/20 dark:via-gray-800 dark:to-rose-800/20">
        <div className="absolute inset-0 bg-gradient-to-br from-rose-500/10 via-transparent to-rose-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 dark:from-rose-400/20 dark:via-transparent dark:to-rose-500/20" />
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
          <CardTitle className="text-sm font-semibold flex items-center gap-2 text-rose-700 dark:text-rose-300">
            <BarChart3 className="h-5 w-5 text-rose-600 dark:text-rose-400" />
            Subjects Tested
          </CardTitle>
          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-rose-500 to-rose-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 dark:from-rose-600 dark:to-rose-700">
            <BookOpen className="h-6 w-6 text-white" />
          </div>
        </CardHeader>
        <CardContent className="relative z-10">
          <div className="text-4xl font-bold text-rose-900 mb-2 dark:text-rose-100">
            {Object.keys(analytics.subjectPerformance).length}
          </div>
          <p className="text-sm text-rose-600/70 font-medium dark:text-rose-300/70">
            {Object.keys(analytics.subjectPerformance).length > 0
              ? (
                <span className="line-clamp-1">
                  {Object.keys(analytics.subjectPerformance).join(', ')}
                </span>
              )
              : 'No subjects tested yet'}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}