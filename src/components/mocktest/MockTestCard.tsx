import { format } from "date-fns";
import { motion } from "framer-motion";
import {
  MoreH<PERSON><PERSON>tal,
  Pencil,
  Trash,
  Award,
  Calendar as CalendarIcon,
  BarChart3,
  FileText,
  CheckCircle,
  ExternalLink,
  Eye,
  Target
} from "lucide-react";
import { MockTest, TestCategory } from "@/types/mockTest";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface MockTestCardProps {
  mockTest: MockTest;
  onEditClick: (mockTest: MockTest) => void;
  onDeleteClick: (mockTestId: string) => void;
  onViewDetails?: (mockTest: MockTest) => void;
  categories?: TestCategory[];
}

export function MockTestCard({
  mockTest,
  onEditClick,
  onDeleteClick,
  onViewDetails,
  categories = []
}: MockTestCardProps) {
  const percentage = mockTest.totalMarks > 0 ? (mockTest.totalMarksObtained / mockTest.totalMarks) * 100 : 0;
  const formattedPercentage = percentage.toFixed(1);

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return null;
    return categories.find(cat => cat.id === categoryId)?.name;
  };

  const getGradeColor = (percentage: number) => {
    if (percentage >= 90) return "bg-green-500";
    if (percentage >= 75) return "bg-green-400";
    if (percentage >= 60) return "bg-yellow-400";
    if (percentage >= 40) return "bg-orange-400";
    return "bg-red-500";
  };



  const getTextColor = (percentage: number) => {
    if (percentage >= 90) return "text-green-500";
    if (percentage >= 75) return "text-green-400";
    if (percentage >= 60) return "text-yellow-500";
    if (percentage >= 40) return "text-orange-500";
    return "text-red-500";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -8 }}
    >
      <Card className="overflow-hidden transition-all duration-500 hover:shadow-2xl border-0 shadow-xl bg-gradient-to-br from-white via-white to-gray-50/30 relative group backdrop-blur-sm dark:from-gray-800 dark:via-gray-800 dark:to-gray-700/50">
        {/* Enhanced gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-transparent to-rose-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 dark:from-violet-400/10 dark:via-transparent dark:to-rose-400/10" />

        {/* Enhanced performance indicator bar */}
        <div
          className={`absolute top-0 left-0 right-0 h-1.5 ${getGradeColor(percentage)} opacity-90 shadow-sm`}
        />

      <CardHeader className="flex flex-col space-y-3 p-6 pb-4 relative z-10">
        <div className="flex justify-between items-start w-full">
          <div className="flex-1 mr-4">
            {/* Enhanced subject badges */}
            <div className="flex flex-wrap gap-2 mb-4">
              {mockTest.subjectMarks.map((subject, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <Badge
                    style={{
                      backgroundColor: `${subject.subjectColor}15`,
                      color: subject.subjectColor,
                      borderColor: `${subject.subjectColor}30`
                    }}
                    className="text-xs font-medium transition-all duration-300 hover:shadow-lg border px-3 py-1.5 rounded-full backdrop-blur-sm hover:backdrop-blur-md"
                  >
                    {subject.subject}
                  </Badge>
                </motion.div>
              ))}
            </div>

            {/* Title and review status */}
            <div className="flex items-center gap-3 mb-3">
              <h3 className="font-bold text-xl line-clamp-1 group-hover:bg-gradient-to-r group-hover:from-violet-600 group-hover:to-purple-600 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300 dark:text-gray-100">
                {mockTest.name}
              </h3>
              {mockTest.isReviewed && (
                <Badge className="text-xs bg-gradient-to-r from-emerald-500 to-emerald-600 text-white border-0 shadow-md dark:from-emerald-600 dark:to-emerald-700">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Reviewed
                </Badge>
              )}
            </div>

            {/* Metadata */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground dark:text-gray-400">
              <div className="flex items-center gap-2 bg-gray-100/50 px-3 py-1.5 rounded-full dark:bg-gray-700/50">
                <CalendarIcon className="h-4 w-4" />
                <span className="font-medium">
                  {mockTest.date ? format(new Date(mockTest.date), "MMM dd") : "No date"}
                </span>
              </div>
              {getCategoryName(mockTest.categoryId) && (
                <Badge variant="secondary" className="text-xs bg-violet-100 text-violet-700 border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-600">
                  {getCategoryName(mockTest.categoryId)}
                </Badge>
              )}
            </div>
          </div>

          {/* Enhanced score circle with 3D effect */}
          <motion.div
            className="relative"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center justify-center h-24 w-24 rounded-full bg-gradient-to-br from-white via-gray-50 to-gray-100 shadow-xl border-4 border-white/80 backdrop-blur-sm dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 dark:border-gray-500/50">
              <span className={`text-xl font-bold ${getTextColor(percentage)} drop-shadow-sm`}>
                {formattedPercentage}%
              </span>
            </div>
            {/* Enhanced performance ring with gradient */}
            <div className="absolute inset-0 rounded-full">
              <div
                className={`absolute inset-0 rounded-full border-4 border-transparent ${getGradeColor(percentage).replace('bg-', 'border-')} opacity-80 shadow-lg`}
                style={{
                  transform: `rotate(${(percentage / 100) * 360}deg)`,
                  background: `conic-gradient(from 0deg, ${getGradeColor(percentage).includes('green') ? '#22c55e' : getGradeColor(percentage).includes('yellow') ? '#eab308' : getGradeColor(percentage).includes('orange') ? '#f97316' : '#ef4444'} ${percentage}%, transparent ${percentage}%)`
                }}
              />
            </div>
          </motion.div>
        </div>
      </CardHeader>

      <CardContent className="p-6 pt-0 relative z-10">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <p className="text-sm font-semibold flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-violet-500 to-violet-600 shadow-md">
                <Award className="h-3.5 w-3.5 text-white" />
              </div>
              Overall Performance
            </p>
            <p className={`text-sm font-bold ${getTextColor(percentage)} drop-shadow-sm`}>{formattedPercentage}%</p>
          </div>
          <div className="relative h-3 w-full rounded-full bg-gray-200/50 overflow-hidden shadow-inner dark:bg-gray-700/50">
            <div
              className={`h-full rounded-full ${getGradeColor(percentage)} shadow-sm transition-all duration-700 ease-out relative overflow-hidden`}
              style={{ width: `${Math.min(100, percentage)}%` }}
            >
              {/* Shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-shimmer" />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Subject-wise Breakdown
          </h4>
          {mockTest.subjectMarks.map((subject, index) => {
            const subjectPercentage = (subject.marksObtained / subject.totalMarks) * 100;
            return (
              <motion.div
                key={index}
                className="space-y-3 p-3 rounded-lg bg-gradient-to-r from-gray-50/50 to-transparent hover:from-gray-100/50 transition-all duration-300 border border-gray-200/30 dark:from-gray-800/30 dark:to-transparent dark:hover:from-gray-700/50 dark:border-gray-700/30"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex justify-between items-center text-sm">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded-full shadow-sm border-2 border-white dark:border-gray-800"
                      style={{ backgroundColor: subject.subjectColor }}
                    ></div>
                    <span className="font-semibold">{subject.subject}</span>
                  </div>
                  <span className="font-bold text-gray-900 dark:text-gray-100">
                    {subject.marksObtained}/{subject.totalMarks} ({subjectPercentage.toFixed(1)}%)
                  </span>
                </div>
                <div className="relative h-2 w-full rounded-full overflow-hidden shadow-inner"
                     style={{ backgroundColor: `${subject.subjectColor}15` }}>
                  <div
                    className="h-full rounded-full transition-all duration-700 ease-out relative overflow-hidden shadow-sm"
                    style={{
                      backgroundColor: subject.subjectColor,
                      width: `${Math.min(100, subjectPercentage)}%`
                    }}
                  >
                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-shimmer" />
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
          <div className="flex justify-between items-center p-3 rounded-lg bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/30 dark:to-transparent">
            <p className="text-sm font-semibold flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-emerald-500 to-emerald-600 shadow-md">
                <Target className="h-3.5 w-3.5 text-white" />
              </div>
              Total Score
            </p>
            <p className="font-bold text-lg text-gray-900 dark:text-gray-100">
              {mockTest.totalMarksObtained} / {mockTest.totalMarks}
            </p>
          </div>
        </div>

        {mockTest.notes && (
          <motion.div
            className="mt-4 pt-4 border-t border-gray-200/50 dark:border-gray-700/50"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="bg-gradient-to-r from-violet-50/50 to-purple-50/30 dark:from-violet-900/20 dark:to-purple-900/10 p-4 rounded-lg border border-violet-200/30 dark:border-violet-700/30">
              <p className="text-sm font-semibold text-violet-700 dark:text-violet-300 mb-2 flex items-center gap-2">
                <div className="p-1 rounded bg-violet-500/20">
                  <FileText className="h-3.5 w-3.5" />
                </div>
                Notes
              </p>
              <p className="text-sm line-clamp-2 text-gray-700 dark:text-gray-300">{mockTest.notes}</p>
            </div>
          </motion.div>
        )}
      </CardContent>

      <CardFooter className="p-6 pt-0 flex justify-between items-center border-t border-gray-200/30 dark:border-gray-700/30 bg-gradient-to-r from-gray-50/30 to-transparent dark:from-gray-800/20 dark:to-transparent">
        <div className="flex gap-3">
          {onViewDetails && (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(mockTest)}
                className="gap-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white border-0 hover:from-violet-600 hover:to-purple-700 shadow-md hover:shadow-lg transition-all duration-300"
              >
                <Eye className="h-4 w-4" />
                View Details
              </Button>
            </motion.div>
          )}
          {mockTest.testPaperUrl && (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(mockTest.testPaperUrl, '_blank')}
                className="gap-2 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white border-0 hover:from-emerald-600 hover:to-emerald-700 shadow-md hover:shadow-lg transition-all duration-300"
              >
                <ExternalLink className="h-4 w-4" />
                Paper
              </Button>
            </motion.div>
          )}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <Button
                variant="outline"
                size="icon"
                className="h-9 w-9 rounded-full border-gray-300/50 hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-200 hover:border-gray-400/50 transition-all duration-300 shadow-sm hover:shadow-md dark:border-gray-600/50 dark:hover:from-gray-700 dark:hover:to-gray-600"
              >
                <MoreHorizontal className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                <span className="sr-only">Actions</span>
              </Button>
            </motion.div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48 border border-gray-200/50 shadow-xl backdrop-blur-sm dark:border-gray-700/50">
            <DropdownMenuItem
              onClick={() => onEditClick(mockTest)}
              className="cursor-pointer hover:bg-gradient-to-r hover:from-violet-50 hover:to-purple-50 hover:text-violet-700 transition-all duration-200 dark:hover:from-violet-900/20 dark:hover:to-purple-900/20 dark:hover:text-violet-300"
            >
              <Pencil className="h-4 w-4 mr-2" />
              Edit Test
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-red-600 cursor-pointer hover:bg-gradient-to-r hover:from-red-50 hover:to-rose-50 dark:hover:from-red-900/20 dark:hover:to-rose-900/20 transition-all duration-200"
              onClick={() => onDeleteClick(mockTest.id)}
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete Test
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
    </motion.div>
  );
}