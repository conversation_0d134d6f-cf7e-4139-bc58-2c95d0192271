import { format } from "date-fns";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Trash,
  Award,
  Calendar as CalendarI<PERSON>,
  BarChart3,
  FileText,
  CheckCircle,
  Clock,
  ExternalLink,
  Eye,
  Target
} from "lucide-react";
import { MockTest, TestCategory } from "@/types/mockTest";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface MockTestCardProps {
  mockTest: MockTest;
  onEditClick: (mockTest: MockTest) => void;
  onDeleteClick: (mockTestId: string) => void;
  onViewDetails?: (mockTest: MockTest) => void;
  categories?: TestCategory[];
}

export function MockTestCard({
  mockTest,
  onEditClick,
  onDeleteClick,
  onViewDetails,
  categories = []
}: MockTestCardProps) {
  const percentage = mockTest.totalMarks > 0 ? (mockTest.totalMarksObtained / mockTest.totalMarks) * 100 : 0;
  const formattedPercentage = percentage.toFixed(1);

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return null;
    return categories.find(cat => cat.id === categoryId)?.name;
  };

  const getGradeColor = (percentage: number) => {
    if (percentage >= 90) return "bg-green-500";
    if (percentage >= 75) return "bg-green-400";
    if (percentage >= 60) return "bg-yellow-400";
    if (percentage >= 40) return "bg-orange-400";
    return "bg-red-500";
  };



  const getTextColor = (percentage: number) => {
    if (percentage >= 90) return "text-green-500";
    if (percentage >= 75) return "text-green-400";
    if (percentage >= 60) return "text-yellow-500";
    if (percentage >= 40) return "text-orange-500";
    return "text-red-500";
  };

  return (
    <Card className="overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 border-0 shadow-lg bg-gradient-to-br from-white via-white to-gray-50/50 relative group backdrop-blur-sm dark:from-gray-800 dark:via-gray-800 dark:to-gray-700/50">
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-transparent to-rose-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 dark:from-violet-400/10 dark:via-transparent dark:to-rose-400/10" />

      {/* Performance indicator bar */}
      <div
        className={`absolute top-0 left-0 right-0 h-1 ${getGradeColor(percentage)} opacity-80`}
      />

      <CardHeader className="flex flex-col space-y-3 p-6 pb-4 relative z-10">
        <div className="flex justify-between items-start w-full">
          <div className="flex-1 mr-4">
            {/* Subject badges */}
            <div className="flex flex-wrap gap-2 mb-4">
              {mockTest.subjectMarks.map((subject, index) => (
                <Badge
                  key={index}
                  style={{
                    backgroundColor: `${subject.subjectColor}15`,
                    color: subject.subjectColor,
                    borderColor: `${subject.subjectColor}30`
                  }}
                  className="text-xs font-medium transition-all duration-300 hover:scale-110 hover:shadow-md border px-3 py-1 rounded-full backdrop-blur-sm"
                >
                  {subject.subject}
                </Badge>
              ))}
            </div>

            {/* Title and review status */}
            <div className="flex items-center gap-3 mb-3">
              <h3 className="font-bold text-xl line-clamp-1 group-hover:bg-gradient-to-r group-hover:from-violet-600 group-hover:to-purple-600 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300 dark:text-gray-100">
                {mockTest.name}
              </h3>
              {mockTest.isReviewed && (
                <Badge className="text-xs bg-gradient-to-r from-emerald-500 to-emerald-600 text-white border-0 shadow-md dark:from-emerald-600 dark:to-emerald-700">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Reviewed
                </Badge>
              )}
            </div>

            {/* Metadata */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground dark:text-gray-400">
              <div className="flex items-center gap-2 bg-gray-100/50 px-3 py-1.5 rounded-full dark:bg-gray-700/50">
                <CalendarIcon className="h-4 w-4" />
                <span className="font-medium">
                  {mockTest.date ? format(new Date(mockTest.date), "MMM dd") : "No date"}
                </span>
              </div>
              {getCategoryName(mockTest.categoryId) && (
                <Badge variant="secondary" className="text-xs bg-violet-100 text-violet-700 border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-600">
                  {getCategoryName(mockTest.categoryId)}
                </Badge>
              )}
            </div>
          </div>

          {/* Score circle */}
          <div className="relative">
            <div className="flex items-center justify-center h-20 w-20 rounded-full bg-gradient-to-br from-white to-gray-100 shadow-lg border-4 border-white group-hover:scale-110 transition-transform duration-300 dark:from-gray-700 dark:to-gray-600 dark:border-gray-600">
              <span className={`text-xl font-bold ${getTextColor(percentage)}`}>
                {formattedPercentage}%
              </span>
            </div>
            {/* Performance ring */}
            <div className="absolute inset-0 rounded-full border-4 border-transparent">
              <div
                className={`absolute inset-0 rounded-full border-4 border-t-transparent border-r-transparent border-b-transparent ${getGradeColor(percentage).replace('bg-', 'border-l-')} opacity-60`}
                style={{ transform: `rotate(${(percentage / 100) * 360}deg)` }}
              />
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-5 pt-0">
        <div className="mb-5">
          <div className="flex justify-between items-center mb-2">
            <p className="text-sm font-medium flex items-center gap-1.5">
              <Award className="h-3.5 w-3.5 text-muted-foreground" />
              Overall Performance
            </p>
            <p className={`text-sm font-medium ${getTextColor(percentage)}`}>{formattedPercentage}%</p>
          </div>
          <div className="relative h-2.5 w-full rounded-full bg-muted overflow-hidden">
            <Progress
              value={percentage}
              className={`h-full rounded-full ${getGradeColor(percentage)}`}
            />
          </div>
        </div>

        <div className="space-y-4">
          {mockTest.subjectMarks.map((subject, index) => {
            const subjectPercentage = (subject.marksObtained / subject.totalMarks) * 100;
            return (
              <div key={index} className="space-y-2 group">
                <div className="flex justify-between items-center text-xs">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full transition-transform duration-300 group-hover:scale-125"
                      style={{ backgroundColor: subject.subjectColor }}
                    ></div>
                    <span className="font-medium">{subject.subject}</span>
                  </div>
                  <span className="font-medium">
                    {subject.marksObtained}/{subject.totalMarks} ({subjectPercentage.toFixed(1)}%)
                  </span>
                </div>
                <div className="relative h-1.5 w-full rounded-full overflow-hidden"
                     style={{ backgroundColor: `${subject.subjectColor}20` }}>
                  <Progress
                    value={subjectPercentage}
                    className="h-full rounded-full transition-all duration-500 ease-in-out"
                    style={{ backgroundColor: subject.subjectColor }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-5 pt-4 border-t border-border/30">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium flex items-center gap-1.5">
              <BarChart3 className="h-3.5 w-3.5 text-muted-foreground" />
              Total Score
            </p>
            <p className="font-medium text-lg">
              {mockTest.totalMarksObtained} / {mockTest.totalMarks}
            </p>
          </div>
        </div>

        {mockTest.notes && (
          <div className="mt-4 pt-4 border-t border-border/30 bg-muted/30 p-4 rounded-lg">
            <p className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1.5">
              <FileText className="h-3.5 w-3.5" />
              Notes
            </p>
            <p className="text-sm line-clamp-2">{mockTest.notes}</p>
          </div>
        )}
      </CardContent>

      <CardFooter className="p-5 pt-0 flex justify-between items-center">
        <div className="flex gap-2">
          {onViewDetails && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetails(mockTest)}
              className="gap-2"
            >
              <Eye className="h-4 w-4" />
              View Details
            </Button>
          )}
          {mockTest.testPaperUrl && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(mockTest.testPaperUrl, '_blank')}
              className="gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Paper
            </Button>
          )}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full border-primary/20 hover:bg-primary/5 hover:border-primary/30 transition-all duration-300"
            >
              <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
              <span className="sr-only">Actions</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48 border border-primary/10">
            <DropdownMenuItem
              onClick={() => onEditClick(mockTest)}
              className="cursor-pointer hover:bg-primary/5 hover:text-primary transition-colors duration-200"
            >
              <Pencil className="h-4 w-4 mr-2" />
              Edit Test
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-red-600 cursor-pointer hover:bg-red-50 dark:hover:bg-red-950/20 transition-colors duration-200"
              onClick={() => onDeleteClick(mockTest.id)}
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete Test
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
}