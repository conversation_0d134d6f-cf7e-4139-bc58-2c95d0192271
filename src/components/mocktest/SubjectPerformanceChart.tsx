import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
} from 'chart.js';
import { Bar, Radar } from 'react-chartjs-2';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MockTest } from '@/types/mockTest';
import { BarChart3, Target } from 'lucide-react';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler
);

interface SubjectPerformanceChartProps {
  tests: MockTest[];
}

export function SubjectPerformanceChart({ tests }: SubjectPerformanceChartProps) {
  // Calculate subject-wise performance
  const subjectData = React.useMemo(() => {
    const subjectMap = new Map<string, { total: number; obtained: number; count: number; color: string }>();
    
    tests.forEach(test => {
      test.subjectMarks.forEach(subject => {
        const existing = subjectMap.get(subject.subject) || { total: 0, obtained: 0, count: 0, color: subject.subjectColor };
        subjectMap.set(subject.subject, {
          total: existing.total + subject.totalMarks,
          obtained: existing.obtained + subject.marksObtained,
          count: existing.count + 1,
          color: subject.subjectColor
        });
      });
    });

    return Array.from(subjectMap.entries()).map(([subject, data]) => ({
      subject,
      percentage: (data.obtained / data.total) * 100,
      averagePercentage: (data.obtained / data.total) * 100,
      totalTests: data.count,
      color: data.color
    }));
  }, [tests]);

  const barChartData = {
    labels: subjectData.map(item => item.subject),
    datasets: [
      {
        label: 'Average Performance (%)',
        data: subjectData.map(item => item.percentage),
        backgroundColor: subjectData.map(item => `${item.color}80`),
        borderColor: subjectData.map(item => item.color),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }
    ]
  };

  const radarChartData = {
    labels: subjectData.map(item => item.subject),
    datasets: [
      {
        label: 'Performance',
        data: subjectData.map(item => item.percentage),
        backgroundColor: 'rgba(139, 92, 246, 0.2)',
        borderColor: 'rgb(139, 92, 246)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(139, 92, 246)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(139, 92, 246)',
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    }
  };

  const radarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `Performance: ${context.parsed.r.toFixed(1)}%`;
          }
        }
      }
    },
    scales: {
      r: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    }
  };

  if (subjectData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Subject Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">No test data available for analysis</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Subject Performance Analysis
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="bar" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="bar" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              Bar Chart
            </TabsTrigger>
            <TabsTrigger value="radar" className="gap-2">
              <Target className="h-4 w-4" />
              Radar Chart
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="bar" className="mt-6">
            <div className="h-80">
              <Bar data={barChartData} options={chartOptions} />
            </div>
          </TabsContent>
          
          <TabsContent value="radar" className="mt-6">
            <div className="h-80">
              <Radar data={radarChartData} options={radarOptions} />
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {subjectData.map((subject, index) => (
            <div key={index} className="p-4 rounded-lg border bg-card">
              <div className="flex items-center gap-2 mb-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: subject.color }}
                />
                <h4 className="font-medium">{subject.subject}</h4>
              </div>
              <p className="text-2xl font-bold" style={{ color: subject.color }}>
                {subject.percentage.toFixed(1)}%
              </p>
              <p className="text-sm text-muted-foreground">
                {subject.totalTests} test{subject.totalTests !== 1 ? 's' : ''}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
