import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
} from 'chart.js';
import { Bar, Radar } from 'react-chartjs-2';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MockTest } from '@/types/mockTest';
import { BarChart3, Target } from 'lucide-react';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler
);

interface SubjectPerformanceChartProps {
  tests: MockTest[];
}

export function SubjectPerformanceChart({ tests }: SubjectPerformanceChartProps) {
  // Calculate subject-wise performance
  const subjectData = React.useMemo(() => {
    const subjectMap = new Map<string, { total: number; obtained: number; count: number; color: string }>();
    
    tests.forEach(test => {
      test.subjectMarks.forEach(subject => {
        const existing = subjectMap.get(subject.subject) || { total: 0, obtained: 0, count: 0, color: subject.subjectColor };
        subjectMap.set(subject.subject, {
          total: existing.total + subject.totalMarks,
          obtained: existing.obtained + subject.marksObtained,
          count: existing.count + 1,
          color: subject.subjectColor
        });
      });
    });

    return Array.from(subjectMap.entries()).map(([subject, data]) => ({
      subject,
      percentage: (data.obtained / data.total) * 100,
      averagePercentage: (data.obtained / data.total) * 100,
      totalTests: data.count,
      color: data.color
    }));
  }, [tests]);

  const barChartData = {
    labels: subjectData.map(item => item.subject),
    datasets: [
      {
        label: 'Average Performance (%)',
        data: subjectData.map(item => item.percentage),
        backgroundColor: subjectData.map(item => `${item.color}80`),
        borderColor: subjectData.map(item => item.color),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }
    ]
  };

  const radarChartData = {
    labels: subjectData.map(item => item.subject),
    datasets: [
      {
        label: 'Performance',
        data: subjectData.map(item => item.percentage),
        backgroundColor: 'rgba(139, 92, 246, 0.2)',
        borderColor: 'rgb(139, 92, 246)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(139, 92, 246)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(139, 92, 246)',
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    }
  };

  const radarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `Performance: ${context.parsed.r.toFixed(1)}%`;
          }
        }
      }
    },
    scales: {
      r: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    }
  };

  if (subjectData.length === 0) {
    return (
      <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-white via-white to-gray-50/30 dark:from-gray-800 dark:via-gray-800 dark:to-gray-700/50">
        <CardHeader className="bg-gradient-to-r from-gray-50/50 to-gray-100/30 border-b border-gray-200/30 backdrop-blur-sm dark:from-gray-700/50 dark:to-gray-600/30 dark:border-gray-600/30">
          <CardTitle className="flex items-center gap-3 text-xl font-bold">
            <div className="p-2 rounded-lg bg-gradient-to-br from-gray-500 to-gray-600 shadow-lg">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <span className="bg-gradient-to-r from-gray-600 to-gray-700 bg-clip-text text-transparent dark:from-gray-300 dark:to-gray-400">
              Subject Performance
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="p-4 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
            <BarChart3 className="h-12 w-12 text-gray-400 dark:text-gray-500" />
          </div>
          <p className="text-gray-500 dark:text-gray-400 text-center">No test data available for analysis</p>
          <p className="text-sm text-gray-400 dark:text-gray-500 text-center mt-2">Complete some mock tests to see your subject performance</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-white via-white to-violet-50/30 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative dark:from-gray-800 dark:via-gray-800 dark:to-gray-700/50">
      <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 dark:from-violet-400/10 dark:via-transparent dark:to-purple-400/10" />
      <CardHeader className="bg-gradient-to-r from-violet-50/50 to-purple-100/30 border-b border-violet-200/30 backdrop-blur-sm relative z-10 dark:from-gray-700/50 dark:to-gray-600/30 dark:border-gray-600/30">
        <CardTitle className="flex items-center gap-3 text-xl font-bold">
          <div className="p-2 rounded-lg bg-gradient-to-br from-violet-500 to-violet-600 shadow-lg dark:from-violet-600 dark:to-violet-700">
            <BarChart3 className="h-6 w-6 text-white" />
          </div>
          <span className="bg-gradient-to-r from-violet-600 to-violet-700 bg-clip-text text-transparent dark:from-violet-400 dark:to-violet-500">
            Subject Performance Analysis
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 relative z-10">
        <Tabs defaultValue="bar" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-muted/30 backdrop-blur-sm border border-border/50 shadow-sm dark:bg-gray-800/30 dark:border-gray-700/50">
            <TabsTrigger
              value="bar"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300 dark:data-[state=active]:from-violet-600 dark:data-[state=active]:to-purple-700"
            >
              <BarChart3 className="h-4 w-4" />
              Bar Chart
            </TabsTrigger>
            <TabsTrigger
              value="radar"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300 dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-emerald-700"
            >
              <Target className="h-4 w-4" />
              Radar Chart
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="bar" className="mt-6">
            <div className="h-96 p-4 rounded-lg bg-gradient-to-br from-white/50 to-gray-50/30 border border-gray-200/30 shadow-inner dark:from-gray-800/50 dark:to-gray-700/30 dark:border-gray-600/30">
              <Bar data={barChartData} options={chartOptions} />
            </div>
          </TabsContent>

          <TabsContent value="radar" className="mt-6">
            <div className="h-96 p-4 rounded-lg bg-gradient-to-br from-white/50 to-gray-50/30 border border-gray-200/30 shadow-inner dark:from-gray-800/50 dark:to-gray-700/30 dark:border-gray-600/30">
              <Radar data={radarChartData} options={radarOptions} />
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Target className="h-5 w-5 text-violet-600 dark:text-violet-400" />
            Subject Summary
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {subjectData.map((subject, index) => (
              <div
                key={index}
                className="p-5 rounded-xl border-0 shadow-lg bg-gradient-to-br from-white via-white to-gray-50/50 hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group relative dark:from-gray-800 dark:via-gray-800 dark:to-gray-700/50"
              >
                <div className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"
                     style={{ background: `linear-gradient(135deg, ${subject.color}05, transparent)` }} />
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-3">
                    <div
                      className="w-5 h-5 rounded-full shadow-md border-2 border-white dark:border-gray-800"
                      style={{ backgroundColor: subject.color }}
                    />
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">{subject.subject}</h4>
                  </div>
                  <p className="text-3xl font-bold mb-2" style={{ color: subject.color }}>
                    {subject.percentage.toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                    {subject.totalTests} test{subject.totalTests !== 1 ? 's' : ''} completed
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
