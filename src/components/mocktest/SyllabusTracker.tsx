import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  BookO<PERSON>, 
  CheckCircle2, 
  Circle, 
  Plus, 
  Edit2, 
  Trash2,
  Save,
  X,
  Target,
  BarChart3,
  FileText
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { TestSyllabus, ChapterProgress } from "@/types/mockTest";
import { syllabusStorage } from "@/utils/mockTestLocalStorage";
import { useSupabaseSubjectStore } from "@/stores/supabaseSubjectStore";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

interface SyllabusTrackerProps {
  testId: string;
  testName: string;
  onProgressUpdate?: (progress: number) => void;
}

export function SyllabusTracker({ testId, testName, onProgressUpdate }: SyllabusTrackerProps) {
  const { subjects } = useSupabaseSubjectStore();
  const [syllabus, setSyllabus] = useState<TestSyllabus | null>(null);
  const [isAddChapterDialogOpen, setIsAddChapterDialogOpen] = useState(false);
  const [editingChapter, setEditingChapter] = useState<ChapterProgress | null>(null);
  const [isEditChapterDialogOpen, setIsEditChapterDialogOpen] = useState(false);

  // Form state for adding/editing chapters
  const [chapterForm, setChapterForm] = useState({
    chapterName: "",
    subject: "",
    notes: "",
  });

  // Load syllabus on component mount
  useEffect(() => {
    loadSyllabus();
  }, [testId]);

  // Update parent component when progress changes
  useEffect(() => {
    if (syllabus) {
      onProgressUpdate?.(syllabus.overallProgress);
    }
  }, [syllabus, onProgressUpdate]);

  const loadSyllabus = () => {
    const existingSyllabus = syllabusStorage.get(testId);
    if (existingSyllabus) {
      setSyllabus(existingSyllabus);
    } else {
      // Initialize empty syllabus
      const newSyllabus: TestSyllabus = {
        testId,
        chapters: [],
        overallProgress: 0,
        lastUpdated: new Date().toISOString(),
      };
      setSyllabus(newSyllabus);
    }
  };

  const resetChapterForm = () => {
    setChapterForm({
      chapterName: "",
      subject: "",
      notes: "",
    });
  };

  const handleAddChapter = () => {
    if (!chapterForm.chapterName.trim() || !chapterForm.subject) {
      toast({
        title: "Error",
        description: "Please enter chapter name and select a subject",
        variant: "destructive",
      });
      return;
    }

    if (!syllabus) return;

    const newChapter: ChapterProgress = {
      chapterId: `chapter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      chapterName: chapterForm.chapterName.trim(),
      subject: chapterForm.subject,
      isCompleted: false,
      notes: chapterForm.notes.trim() || undefined,
    };

    const updatedSyllabus: TestSyllabus = {
      ...syllabus,
      chapters: [...syllabus.chapters, newChapter],
    };

    syllabusStorage.save(updatedSyllabus);
    setSyllabus(updatedSyllabus);
    setIsAddChapterDialogOpen(false);
    resetChapterForm();

    toast({
      title: "Success",
      description: "Chapter added successfully",
    });
  };

  const handleEditChapter = () => {
    if (!editingChapter || !chapterForm.chapterName.trim() || !chapterForm.subject) {
      toast({
        title: "Error",
        description: "Please enter chapter name and select a subject",
        variant: "destructive",
      });
      return;
    }

    if (!syllabus) return;

    const updatedChapters = syllabus.chapters.map(chapter =>
      chapter.chapterId === editingChapter.chapterId
        ? {
            ...chapter,
            chapterName: chapterForm.chapterName.trim(),
            subject: chapterForm.subject,
            notes: chapterForm.notes.trim() || undefined,
          }
        : chapter
    );

    const updatedSyllabus: TestSyllabus = {
      ...syllabus,
      chapters: updatedChapters,
    };

    syllabusStorage.save(updatedSyllabus);
    setSyllabus(updatedSyllabus);
    setIsEditChapterDialogOpen(false);
    setEditingChapter(null);
    resetChapterForm();

    toast({
      title: "Success",
      description: "Chapter updated successfully",
    });
  };

  const handleDeleteChapter = (chapterId: string) => {
    if (!syllabus) return;

    const updatedChapters = syllabus.chapters.filter(chapter => chapter.chapterId !== chapterId);
    const updatedSyllabus: TestSyllabus = {
      ...syllabus,
      chapters: updatedChapters,
    };

    syllabusStorage.save(updatedSyllabus);
    setSyllabus(updatedSyllabus);

    toast({
      title: "Success",
      description: "Chapter deleted successfully",
    });
  };

  const toggleChapterCompletion = (chapterId: string) => {
    if (!syllabus) return;

    const updatedChapters = syllabus.chapters.map(chapter =>
      chapter.chapterId === chapterId
        ? {
            ...chapter,
            isCompleted: !chapter.isCompleted,
            completedAt: !chapter.isCompleted ? new Date().toISOString() : undefined,
          }
        : chapter
    );

    const updatedSyllabus: TestSyllabus = {
      ...syllabus,
      chapters: updatedChapters,
    };

    syllabusStorage.save(updatedSyllabus);
    setSyllabus(updatedSyllabus);
  };

  const openEditChapterDialog = (chapter: ChapterProgress) => {
    setEditingChapter(chapter);
    setChapterForm({
      chapterName: chapter.chapterName,
      subject: chapter.subject,
      notes: chapter.notes || "",
    });
    setIsEditChapterDialogOpen(true);
  };

  // Group chapters by subject
  const chaptersBySubject = syllabus?.chapters.reduce((acc, chapter) => {
    if (!acc[chapter.subject]) {
      acc[chapter.subject] = [];
    }
    acc[chapter.subject].push(chapter);
    return acc;
  }, {} as Record<string, ChapterProgress[]>) || {};

  // Calculate subject-wise progress
  const subjectProgress = Object.entries(chaptersBySubject).map(([subject, chapters]) => {
    const completedChapters = chapters.filter(ch => ch.isCompleted).length;
    const progress = chapters.length > 0 ? (completedChapters / chapters.length) * 100 : 0;
    return { subject, total: chapters.length, completed: completedChapters, progress };
  });

  if (!syllabus) {
    return <div>Loading syllabus...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header with overall progress */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Syllabus Progress for {testName}
            </CardTitle>
            <Dialog open={isAddChapterDialogOpen} onOpenChange={setIsAddChapterDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetChapterForm} size="sm" className="gap-2">
                  <Plus className="h-4 w-4" />
                  Add Chapter
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add Chapter</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="chapter-name">Chapter Name *</Label>
                    <Input
                      id="chapter-name"
                      value={chapterForm.chapterName}
                      onChange={(e) => setChapterForm(prev => ({ ...prev, chapterName: e.target.value }))}
                      placeholder="e.g., Thermodynamics, Organic Chemistry"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="chapter-subject">Subject *</Label>
                    <Select
                      value={chapterForm.subject}
                      onValueChange={(value) => setChapterForm(prev => ({ ...prev, subject: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.name}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="chapter-notes">Notes</Label>
                    <Textarea
                      id="chapter-notes"
                      value={chapterForm.notes}
                      onChange={(e) => setChapterForm(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Optional notes about this chapter"
                      rows={3}
                    />
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddChapter} className="flex-1">
                      Add Chapter
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsAddChapterDialogOpen(false)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">
                {syllabus.chapters.filter(ch => ch.isCompleted).length} / {syllabus.chapters.length} chapters
              </span>
            </div>
            <Progress value={syllabus.overallProgress} className="h-2" />
            <div className="text-center">
              <Badge variant={syllabus.overallProgress === 100 ? "default" : "secondary"}>
                {syllabus.overallProgress}% Complete
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subject-wise progress */}
      {subjectProgress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Subject-wise Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {subjectProgress.map(({ subject, total, completed, progress }) => (
                <div key={subject} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{subject}</span>
                    <span className="text-sm text-muted-foreground">
                      {completed}/{total}
                    </span>
                  </div>
                  <Progress value={progress} className="h-2" />
                  <div className="text-center">
                    <Badge variant="outline" className="text-xs">
                      {Math.round(progress)}%
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chapters by subject */}
      {Object.keys(chaptersBySubject).length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No chapters added yet</h3>
            <p className="text-muted-foreground text-center mb-4">
              Add chapters to track your syllabus completion for this test
            </p>
            <Button onClick={() => setIsAddChapterDialogOpen(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              Add Your First Chapter
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Chapters by Subject
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Accordion type="multiple" className="w-full">
              {Object.entries(chaptersBySubject).map(([subject, chapters]) => (
                <AccordionItem key={subject} value={subject}>
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center justify-between w-full mr-4">
                      <span className="font-medium">{subject}</span>
                      <Badge variant="outline">
                        {chapters.filter(ch => ch.isCompleted).length}/{chapters.length}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3 pt-2">
                      {chapters.map((chapter, index) => (
                        <motion.div
                          key={chapter.chapterId}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className={cn(
                            "flex items-center justify-between p-3 rounded-lg border transition-colors",
                            chapter.isCompleted 
                              ? "bg-green-50 border-green-200" 
                              : "bg-gray-50 border-gray-200"
                          )}
                        >
                          <div className="flex items-center gap-3 flex-1">
                            <button
                              onClick={() => toggleChapterCompletion(chapter.chapterId)}
                              className="flex-shrink-0"
                            >
                              {chapter.isCompleted ? (
                                <CheckCircle2 className="h-5 w-5 text-green-600" />
                              ) : (
                                <Circle className="h-5 w-5 text-gray-400" />
                              )}
                            </button>
                            <div className="flex-1">
                              <h4 className={cn(
                                "font-medium",
                                chapter.isCompleted && "line-through text-muted-foreground"
                              )}>
                                {chapter.chapterName}
                              </h4>
                              {chapter.notes && (
                                <p className="text-sm text-muted-foreground mt-1">
                                  {chapter.notes}
                                </p>
                              )}
                              {chapter.completedAt && (
                                <p className="text-xs text-green-600 mt-1">
                                  Completed on {new Date(chapter.completedAt).toLocaleDateString()}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditChapterDialog(chapter)}
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteChapter(chapter.chapterId)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>
      )}

      {/* Edit Chapter Dialog */}
      <Dialog open={isEditChapterDialogOpen} onOpenChange={setIsEditChapterDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Chapter</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-chapter-name">Chapter Name *</Label>
              <Input
                id="edit-chapter-name"
                value={chapterForm.chapterName}
                onChange={(e) => setChapterForm(prev => ({ ...prev, chapterName: e.target.value }))}
                placeholder="e.g., Thermodynamics, Organic Chemistry"
              />
            </div>
            
            <div>
              <Label htmlFor="edit-chapter-subject">Subject *</Label>
              <Select
                value={chapterForm.subject}
                onValueChange={(value) => setChapterForm(prev => ({ ...prev, subject: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.name}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="edit-chapter-notes">Notes</Label>
              <Textarea
                id="edit-chapter-notes"
                value={chapterForm.notes}
                onChange={(e) => setChapterForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Optional notes about this chapter"
                rows={3}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button onClick={handleEditChapter} className="flex-1">
                Update Chapter
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsEditChapterDialogOpen(false);
                  setEditingChapter(null);
                  resetChapterForm();
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
